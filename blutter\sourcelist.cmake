set(SRCS 
    CodeAnalyzer.cpp
    CodeAnalyzer.h
    CodeAnalyzer_arm64.cpp
    DartApp.cpp
    DartApp.h
    DartClass.cpp
    DartClass.h
    DartDumper.cpp
    DartDumper.h
    DartField.cpp
    DartField.h
    DartFnBase.h
    DartFunction.cpp
    DartFunction.h
    DartLibrary.cpp
    DartLibrary.h
    DartLoader.cpp
    DartLoader.h
    DartStub.cpp
    DartStub.h
    DartThreadInfo.cpp
    DartThreadInfo.h
    DartTypes.cpp
    DartTypes.h
    Disassembler.cpp
    Disassembler.h
    Disassembler_arm64.cpp
    Disassembler_arm64.h
    ElfHelper.cpp
    ElfHelper.h
    FridaWriter.cpp
    FridaWriter.h
    HtArrayIterator.h
    Util.cpp
    Util.h
    VarValue.cpp
    VarValue.h
    args.hxx
    il.cpp
    il.h
    main.cpp
    #pch.cpp
    pch.h
)
