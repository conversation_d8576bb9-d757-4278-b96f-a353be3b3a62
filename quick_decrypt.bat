@echo off
chcp 65001 >nul
title Blutter 快速解密工具
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        Blutter 快速解密工具                                  ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM 检查是否有拖拽的文件参数
if "%~1"=="" (
    echo 使用方法：
    echo 1. 直接拖拽 APK 文件或 lib 目录到此批处理文件上
    echo 2. 或者运行此文件后手动输入路径
    echo.
    echo 请输入要解密的文件或目录路径：
    set /p input_path="路径: "
) else (
    set input_path=%~1
    echo 检测到拖拽文件: %input_path%
)

REM 检查输入路径是否存在
if not exist "%input_path%" (
    echo.
    echo ❌ 错误：找不到指定的文件或目录！
    pause
    exit /b 1
)

REM 生成输出目录名
for %%F in ("%input_path%") do set filename=%%~nF
if "%filename%"=="" set filename=input
set timestamp=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set timestamp=%timestamp: =0%
set output_dir=output_%filename%_%timestamp%

echo.
echo 输入文件/目录: %input_path%
echo 输出目录: %output_dir%
echo.
echo 开始解密处理...
echo ╔══════════════════════════════════════════════════════════════════════════════╗

REM 执行 blutter
python blutter.py "%input_path%" "%output_dir%"

echo ╚══════════════════════════════════════════════════════════════════════════════╝

if %errorlevel% equ 0 (
    echo.
    echo ✅ 解密完成！
    echo.
    echo 输出目录: %output_dir%
    echo.
    echo 生成的文件：
    echo   📁 asm/              - 反编译的 Dart 汇编文件
    echo   📄 blutter_frida.js  - Frida 脚本模板
    echo   📄 objs.txt          - 对象池转储
    echo   📄 pp.txt            - Dart 对象池信息
    echo   📁 ida_script/       - IDA Pro 脚本
    echo.
    
    REM 自动打开输出文件夹
    echo 正在打开输出文件夹...
    explorer "%output_dir%"
    
    echo.
    echo 🎉 处理完成！可以开始分析了。
) else (
    echo.
    echo ❌ 解密失败！
    echo.
    echo 可能的原因：
    echo   - 输入文件不是有效的 APK 或 lib 目录
    echo   - 缺少必要的依赖库
    echo   - 系统环境配置问题
    echo.
    echo 请检查上方的错误信息，或使用完整版 GUI 工具进行诊断。
)

echo.
pause
